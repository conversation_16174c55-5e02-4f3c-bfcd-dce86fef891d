# 分时段获取推文功能说明

## 问题背景

Twitter API 存在一个重要限制：**只能获取用户最近的约 3200 条推文**。这意味着：

- 如果用户有 22,000 条推文，正常情况下只能获取最近的 3200 条左右
- 更早的历史推文无法通过常规 API 获取
- 这是 Twitter 平台的硬性限制，不是 tmd 程序的问题

## 解决方案：分时段获取

tmd 现在提供了 `--time-segments` 参数来尝试突破这个限制：

### 工作原理

1. **智能时间范围检测**：
   - 首先获取用户最近的推文，找到最早推文的时间
   - 以此时间为基准，向前推算更早的时间段

2. **分段获取策略**：
   - 将历史时间分成多个时间段（默认10段）
   - 对每个时间段单独发起 API 请求
   - 通过设置 `TimeRange{Min, Max}` 来"欺骗" API 返回特定时间段的推文

3. **智能停止机制**：
   - 如果某个时间段返回空结果，自动停止获取
   - 避免无效的 API 请求

### 使用方法

```bash
# 启用分时段获取模式
./tmd --user Crypto_Cat888 --all-tweets --time-segments

# 只获取媒体推文的分时段模式
./tmd --user Crypto_Cat888 --time-segments
```

### 效果对比

**正常模式**：
- 获取推文数量：约 763 条
- 时间范围：2025-08-10 到 2025-10-05（约2个月）

**分时段模式**：
- 理论上可以获取更多历史推文
- 时间范围可能扩展到数年前
- 具体效果取决于 Twitter API 的实际限制

### 注意事项

1. **不是万能解决方案**：
   - Twitter API 的限制是服务器端的，分时段获取只是一种尝试
   - 某些非常老的推文可能仍然无法获取

2. **请求频率控制**：
   - 每个时间段之间会自动等待 3 秒
   - 避免触发 Twitter 的速率限制

3. **适用场景**：
   - 仅在第一次获取用户推文时启用
   - 推文数量超过 3000 条时自动激活
   - 后续增量更新仍使用正常模式

4. **性能影响**：
   - 分时段获取需要更多时间
   - 会发起更多 API 请求
   - 建议在网络稳定时使用

### 调试信息

启用分时段模式后，你会看到详细的调试信息：

```
🔍 先获取最近推文来估算时间范围...
📊 最早推文时间: 2025-08-10 13:16:15，将从此时间开始分段
🔄 开始获取更早的推文，共10个时间段
📅 获取时间段 1/10: 2023-08-10 到 2024-02-10
✅ 时间段 1 获取到 156 条推文
📅 获取时间段 2/10: 2024-02-10 到 2024-08-10
✅ 时间段 2 获取到 234 条推文
...
🎉 分时段获取完成，总计 1543 条推文
```

### 技术原理

分时段获取的核心是利用 Twitter API 的时间过滤功能：

```go
timeRange := &utils.TimeRange{
    Min: segmentStart,  // 时间段开始
    Max: segmentEnd,    // 时间段结束
}
```

通过设置不同的时间窗口，强制 API 返回特定时间段的推文，从而绕过单次请求的历史推文限制。

### 总结

分时段获取是一个实验性功能，旨在尽可能多地获取用户的历史推文。虽然不能保证突破所有限制，但在很多情况下可以显著增加获取到的推文数量。
